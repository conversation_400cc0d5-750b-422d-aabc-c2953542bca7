// ==UserScript==
// @name         网络记事本
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  基于坚果云WebDAV的网络记事本，随时记录想法
// <AUTHOR> name
// @match        *://*/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_addStyle
// @grant        GM_registerMenuCommand
// @grant        GM_notification
// @grant        GM_xmlhttpRequest
// @connect      dav.jianguoyun.com
// @connect      jianguoyun.com
// ==/UserScript==

/*
功能说明：
1. 记事本功能：
   - 网络记事本，随时记录想法
   - 自动保存到坚果云 /notes/ 目录
   - 支持新建、保存、加载笔记
   - 笔记列表按时间排序显示

2. 同步功能：
   - 使用坚果云WebDAV自动同步
   - 支持手动触发同步
   - 多设备数据自动合并

3. 数据保护：
   - 云端数据版本控制
   - 防止意外数据丢失

使用说明：
1. 点击"记事本"按钮打开网络记事本功能
2. 同步功能已预设坚果云账号，无需配置
3. 点击"同步设置"可查看同步状态和日志
*/

GM_addStyle(`
    /* 记事本相关样式 */
    .ya-notepad-dialog {
        width: 900px;
        max-width: 95vw;
        max-height: 90vh;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10000;
        overflow: auto;
        padding: 20px;
    }

    .ya-notepad-form {
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .ya-notepad-form h3 {
        margin: 0 0 20px 0;
        text-align: center;
        color: #333;
    }

    .ya-notepad-form textarea {
        width: 100%;
        min-height: 400px; /* 增加高度 */
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;
        resize: vertical;
    }

    .ya-notepad-form .form-btns {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
    }

    .ya-notepad-form .sync-info {
        margin-top: 15px;
        font-size: 12px;
        color: #666;
        text-align: center;
    }

    .ya-notepad-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 15px;
        margin: 15px 0;
    }

    .ya-notepad-item {
        padding: 10px;
        border: 1px solid #eee;
        border-radius: 8px;
        cursor: pointer;
        transition: background-color 0.2s;
        position: relative; /* 用于定位删除按钮 */
    }

    .ya-notepad-item:hover {
        background-color: #f8f9fa;
    }


    .ya-notepad-item .note-info {
    }

    .ya-notepad-item .note-actions {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    .ya-notepad-item .delete-btn {
        z-index: 1; /* 确保删除按钮在最上层 */
        background: #dc3545; /* 红色背景 */
        color: white;
        border: none;
        padding: 2px 6px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: background-color 0.2s;
    }

    .ya-notepad-item .delete-btn:hover {
        background: #c82333;
    }

    .ya-notepad-item .note-title {
        font-weight: bold;
        color: #333;
        margin-bottom: 2px;
    }

    .ya-notepad-item .note-preview {
        font-size: 11px;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ya-notepad-item .note-time {
        font-size: 11px;
        color: #999;
        margin-top: 2px;
    }

    .ya-btn {
        background: #f17c67;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 14px;
        position: relative;
        overflow: hidden;
    }

    .ya-btn:hover {
        background: #e76450;
    }

    .ya-btn:active {
        transform: scale(0.98);
    }

    .ya-btn:after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, .5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }

    .ya-btn:active:after {
        animation: ripple 0.4s ease-out;
    }

    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }

    .ya-close-btn {
        position: absolute;
        right: 10px;
        top: 0px;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #333;
    }

    .ya-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: 9999;
    }

    /* 配置对话框样式 */
    .ya-config-dialog {
        width: 800px;
        max-width: 90vw;
        max-height: 90vh;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10000;
        overflow: auto;
        padding: 20px;
    }

    .ya-config-form {
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .ya-config-form h3 {
        margin: 0 0 20px 0;
        text-align: center;
    }

    .ya-config-form .form-item {
        margin-bottom: 15px;
    }

    .ya-config-form label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .ya-config-form input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
    }

    .ya-config-form .form-btns {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
    }

    .ya-config-form .sync-info {
        margin-top: 15px;
        font-size: 12px;
        color: #666;
        text-align: center;
    }

    .sync-log {
        margin-top: 15px;
        border-top: 1px solid #eee;
        padding-top: 10px;
    }

    .sync-log h4 {
        margin: 0 0 8px 0;
        color: #666;
        font-size: 13px;
    }

    .log-content {
        max-height: 150px;
        overflow-y: auto;
        font-size: 12px;
    }

    .log-item {
        padding: 3px 8px;
        border-bottom: 1px solid #f5f5f5;
        display: flex;
        align-items: center;
    }

    .log-item .time {
        color: #999;
        margin-right: 8px;
        font-size: 11px;
        white-space: nowrap;
    }

    .log-item .message {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .log-item.success .message {
        color: #28a745;
    }

    .log-item.error .message {
        color: #dc3545;
    }

    .log-item.info .message {
        color: #17a2b8;
    }

    .ya-notepad-container {
        position: fixed;
        right: 20px;
        top: 40px;
        z-index: 9999;
        display: flex;
        gap: 10px;
    }
`);

(() => {
    'use strict';

    class WebDAVSync {
        constructor() {
            this.config = this.loadConfig();
            this.AUTO_SYNC = false;    // 默认关闭自动同步
        }

        loadConfig() {
            this.AUTO_SYNC = GM_getValue('auto_sync_enabled', false);      // 默认关闭
            return {
                server: 'https://dav.jianguoyun.com/dav',
                username: '<EMAIL>',
                password: 'asu9747wedhvcigy',
                lastSync: GM_getValue('webdav_last_sync', null)
            };
        }

        saveConfig(config) {
            GM_setValue('webdav_server', config.server);
            GM_setValue('webdav_username', config.username);
            GM_setValue('webdav_password', config.password);
            this.config = config;
        }

        createDialog(content) {
            const mask = document.createElement('div');
            mask.className = 'ya-mask';

            const dialog = document.createElement('div');
            dialog.className = 'ya-config-dialog';
            dialog.innerHTML = `
                <button class="ya-close-btn">×</button>
                ${content}
            `;

            // 添加关闭按钮事件
            dialog.querySelector('.ya-close-btn').onclick = () => {
                mask.remove();
                dialog.remove();
            };

            document.body.appendChild(mask);
            return dialog;
        }

        showConfigDialog() {
            // 先渲染载入中
            const html = `
                <div class="ya-config-form">
                    <h3>坚果云同步状态</h3>
                    <!-- 同步状态面板 -->
                    <div class="sync-status-panel">
                        <div class="status-item">
                            <span class="label">同步状态:</span>
                            <span class="value ${this.config.lastSync ? 'success' : 'warning'}">
                                ${this.config.lastSync ? '已连接' : '未连接'}
                            </span>
                        </div>
                        <div class="status-item">
                            <span class="label">上次同步:</span>
                            <span class="value">
                                ${this.config.lastSync ? new Date(this.config.lastSync).toLocaleString() : '从未同步'}
                            </span>
                        </div>
                        <div class="status-item">
                            <span class="label">坚果云账号:</span>
                            <span class="value"><EMAIL></span>
                        </div>
                        <div class="status-item">
                            <span class="label">云端地址:</span>
                            <span class="value">
                                <a href="https://www.jianguoyun.com/#/safety" target="_blank">查看坚果云文件</a>
                            </span>
                        </div>
                    </div>
                    <!-- 添加同步设置 -->
                    <div class="sync-settings">
                        <h4>同步设置</h4>
                        <div class="setting-item">
                            <label for="auto_sync">
                                启用自动同步
                            </label>
                            <input type="checkbox" id="auto_sync" ${GM_getValue('auto_sync_enabled', false) ? 'checked' : ''}>
                        </div>
                        <div class="setting-desc">勾选后，每30分钟会自动将本地笔记与云端坚果云同步，保持多设备一致。</div>
                    </div>
                    <!-- 修改操作按钮部分 -->
                    <div class="form-btns">
                        <button class="ya-btn primary" id="test_webdav">测试连接</button>
                    </div>
                    <!-- 同步日志 -->
                    <div class="sync-log">
                        <h4>同步记录 (最近10条)</h4>
                        <div class="log-content">
                            ${this.getSyncLogs()
                                .slice(0, 10)
                                .map(log => `
                                    <div class="log-item ${log.type}">
                                        <span class="time">${new Date(log.time).toLocaleString()}</span>
                                        <span class="message">${log.message}</span>
                                    </div>
                                `).join('')
                                || '暂无同步记录'
                            }
                        </div>
                    </div>
                </div>
            `;

            // 在对话框创建后，立即应用内联样式
            const dialog = this.createDialog(html);
            document.body.appendChild(dialog);

            // 绑定事件
            dialog.querySelector('#test_webdav').onclick = () => this.testConnection(dialog);

            // 绑定自动同步开关事件
            const autoSyncCheckbox = dialog.querySelector('#auto_sync');
            if (autoSyncCheckbox) {
                autoSyncCheckbox.onchange = (e) => {
                    this.AUTO_SYNC = e.target.checked;
                    GM_setValue('auto_sync_enabled', this.AUTO_SYNC);
                    GM_notification({
                        text: `自动同步已${this.AUTO_SYNC ? '开启' : '关闭'}`,
                        timeout: 2000
                    });
                };
            }
        }

        // 显示记事本对话框
        showNotepadDialog() {
            const html = `
                <div class="ya-notepad-form">
                    <h3>网络记事本</h3>
                    <div class="notepad-controls">
                        <button class="ya-btn" id="new-note">新建笔记</button>
                        <button class="ya-btn" id="load-notes">加载笔记列表</button>
                        <button class="ya-btn" id="save-note">保存当前笔记</button>
                        <button class="ya-btn" id="upload-file-btn">上传文件</button>
                        <input type="file" id="file-input" style="display:none;" />
                    </div>
                    <div class="form-item">
                        <label for="note-title">笔记标题:</label>
                        <input type="text" id="note-title" placeholder="输入笔记标题..." />
                    </div>
                    <div class="form-item">
                        <label for="note-content">笔记内容:</label>
                        <textarea id="note-content" placeholder="在这里输入您的笔记内容..."></textarea>
                    </div>
                    <div class="notepad-list-container">
                        <h4>已保存的笔记:</h4>
                        <div class="ya-notepad-list" id="notes-list">
                            <div style="color:#888;text-align:center;padding:20px 0;">点击"加载笔记列表"查看已保存的笔记</div>
                        </div>
                    </div>
                    <div class="form-btns">
                        <button class="ya-btn" onclick="this.closest('.ya-notepad-dialog').remove();document.querySelector('.ya-mask').remove();">关闭</button>
                    </div>
                    <div class="sync-info">
                        <p>笔记将自动保存到坚果云 /notes/ 目录下</p>
                        <p>文件名格式: YYYYMMDD_HHMMSS_标题.txt</p>
                    </div>
                </div>
            `;

            const dialog = document.createElement('div');
            dialog.className = 'ya-notepad-dialog';
            dialog.innerHTML = `
                <button class="ya-close-btn">×</button>
                ${html}
            `;

            const mask = document.createElement('div');
            mask.className = 'ya-mask';

            // 添加关闭按钮事件
            dialog.querySelector('.ya-close-btn').onclick = () => {
                mask.remove();
                dialog.remove();
            };

            // 绑定按钮事件
            dialog.querySelector('#new-note').onclick = () => this.newNote(dialog);
            dialog.querySelector('#load-notes').onclick = () => this.loadNotesList(dialog);
            dialog.querySelector('#save-note').onclick = () => this.saveNote(dialog);
            dialog.querySelector('#upload-file-btn').onclick = () => {
                dialog.querySelector('#file-input').click();
            };
            dialog.querySelector('#file-input').onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;
                try {
                    await this.uploadFile(file);
                    GM_notification({ text: '文件上传成功', timeout: 2000 });
                    this.loadNotesList(dialog);
                } catch (err) {
                    GM_notification({ text: '文件上传失败: ' + err.message, timeout: 3000 });
                }
            };

            document.body.appendChild(mask);
            document.body.appendChild(dialog);

            // 自动加载笔记列表
            this.loadNotesList(dialog);
        }

        // 新建笔记
        newNote(dialog) {
            dialog.querySelector('#note-title').value = '';
            dialog.querySelector('#note-content').value = '';
            GM_notification({ text: '已清空编辑器，可以开始新建笔记', timeout: 2000 });
        }

        // 保存笔记
        async saveNote(dialog) {
            const title = dialog.querySelector('#note-title').value.trim();
            const content = dialog.querySelector('#note-content').value.trim();

            if (!title) {
                GM_notification({ text: '请输入笔记标题', timeout: 2000 });
                return;
            }

            if (!content) {
                GM_notification({ text: '请输入笔记内容', timeout: 2000 });
                return;
            }

            try {
                const timestamp = this.getTimeStampString();
                const filename = `${timestamp}_${title.replace(/[^\w\u4e00-\u9fa5]/g, '_')}.txt`;
                const noteData = `标题: ${title}\n创建时间: ${new Date().toLocaleString()}\n\n${content}`;

                // 确保notes目录存在
                await this.createDirectory('notes');

                // 保存笔记文件
                await this.putFile(`notes/${filename}`, noteData);

                GM_notification({ text: '笔记保存成功！', timeout: 2000 });

                // 重新加载笔记列表
                this.loadNotesList(dialog);

            } catch (error) {
                console.error('保存笔记失败:', error);
                GM_notification({ text: '保存笔记失败: ' + error.message, timeout: 3000 });
            }
        }

        // 加载笔记列表
        async loadNotesList(dialog) {
            const notesList = dialog.querySelector('#notes-list');
            notesList.innerHTML = '<div style="color:#888;text-align:center;padding:20px 0;">加载中...</div>';

            try {
                // 确保notes目录存在
                await this.createDirectory('notes');

                const files = await this.listFiles('notes');
                const noteFiles = files.filter(file => file.name.endsWith('.txt'));
                const otherFiles = files.filter(file => !file.name.endsWith('.txt'));

                if (noteFiles.length === 0 && otherFiles.length === 0) {
                    notesList.innerHTML = '<div style="color:#888;text-align:center;padding:20px 0;">暂无保存的笔记或文件</div>';
                    return;
                }

                // 按修改时间排序（最新的在前）
                noteFiles.sort((a, b) => new Date(b.lastModified) - new Date(a.lastModified));

                const notesHtml = noteFiles.map(file => {
                    const titleMatch = file.name.match(/\d{8}_\d{6}_(.+)\.txt$/);
                    const displayTitle = titleMatch ? titleMatch[1].replace(/_/g, ' ') : file.name;

                    return `
                        <div class="ya-notepad-item" data-filename="${file.name}">
                            <div class="note-info">
                                <div class="note-title">${displayTitle}</div>
                                <div class="note-preview">点击查看内容...</div>
                                <div class="note-time">${new Date(file.lastModified).toLocaleString()}</div>
                            </div>
                            <div class="note-actions">
                                <button class="delete-btn" data-filename="${file.name}">删除</button>
                            </div>
                        </div>
                    `;
                }).join('');

                const otherFilesHtml = otherFiles.length > 0 ? '<h4>已上传的其他文件:</h4>' + otherFiles.map(file => `
                    <div class="ya-notepad-item" data-filename="${file.name}">
                        <div class="note-info">
                            <div class="note-title">${file.name}</div>
                            <div class="note-time">${new Date(file.lastModified).toLocaleString()}</div>
                        </div>
                        <div class="note-actions">
                            <button class="ya-btn download-btn" data-filename="${file.name}">下载</button>
                            <button class="delete-btn" data-filename="${file.name}">删除</button>
                        </div>
                    </div>
                `).join('') : '';
                notesList.innerHTML = notesHtml + otherFilesHtml;

                // 绑定加载笔记和删除笔记事件
                notesList.querySelectorAll('.ya-notepad-item').forEach(item => {
                    if (item.querySelector('.note-info')) {
                        const filename = item.getAttribute('data-filename');
                        if (filename.endsWith('.txt')) {
                            item.querySelector('.note-info').onclick = () => this.loadNote(dialog, filename);
                        }
                        const downloadBtn = item.querySelector('.download-btn');
                        if (downloadBtn) {
                            downloadBtn.onclick = async (e) => {
                                e.stopPropagation();
                                try {
                                    const data = await this.downloadFile(filename);
                                    const blob = new Blob([data]);
                                    const url = URL.createObjectURL(blob);
                                    const a = document.createElement('a');
                                    a.href = url;
                                    a.download = filename;
                                    document.body.appendChild(a);
                                    a.click();
                                    setTimeout(() => {
                                        URL.revokeObjectURL(url);
                                        a.remove();
                                    }, 1000);
                                } catch (err) {
                                    GM_notification({ text: '下载失败: ' + err.message, timeout: 3000 });
                                }
                            };
                        }
                        item.querySelector('.delete-btn').onclick = (e) => {
                            e.stopPropagation(); // 阻止事件冒泡到父级，避免触发加载笔记
                            this.deleteNote(dialog, filename);
                        };
                    }
                });

            } catch (error) {
                console.error('加载笔记列表失败:', error);
                notesList.innerHTML = '<div style="color:#f00;text-align:center;padding:20px 0;">加载失败: ' + error.message + '</div>';
            }
        }

        // 加载指定笔记
        async loadNote(dialog, filename) {
            try {
                const content = await this.getFile(`notes/${filename}`);
                if (!content) {
                    GM_notification({ text: '无法读取笔记内容', timeout: 2000 });
                    return;
                }

                // 解析笔记内容
                const lines = content.split('\n');
                let title = '';
                let noteContent = '';
                let contentStartIndex = 0;

                // 查找标题
                for (let i = 0; i < lines.length; i++) {
                    if (lines[i].startsWith('标题: ')) {
                        title = lines[i].substring(3).trim();
                        contentStartIndex = i + 3; // 跳过标题、时间和空行
                        break;
                    }
                }

                // 获取笔记内容
                if (contentStartIndex < lines.length) {
                    noteContent = lines.slice(contentStartIndex).join('\n');
                }

                // 填充到编辑器
                dialog.querySelector('#note-title').value = title;
                dialog.querySelector('#note-content').value = noteContent;

                GM_notification({ text: '笔记已加载到编辑器', timeout: 2000 });

            } catch (error) {
                console.error('加载笔记失败:', error);
                GM_notification({ text: '加载笔记失败: ' + error.message, timeout: 3000 });
            }
        }

        async testConnection(dialog) {
            try {
                GM_notification({ text: '正在测试连接...', timeout: 2000 });
                const response = await this.propfind(
                    this.config.server,
                    this.config.username,
                    this.config.password
                );

                this.addSyncLog('success', '坚果云连接测试成功');

                const successDialog = this.createDialog(`
                    <div class="ya-config-form">
                        <h3>连接测试成功</h3>
                        <div class="test-result success">
                            <p>✅ 服务器连接正常</p>
                            <p>✅ 账号验证通过</p>
                            <p>✅ WebDAV 服务可用</p>
                        </div>
                        <div class="form-btns">
                            <button class="ya-btn success" onclick="this.closest('.ya-config-dialog').remove();document.querySelector('.ya-mask').remove();">确定</button>
                        </div>
                    </div>
                `);
                document.body.appendChild(successDialog);
            } catch (error) {
                console.error('测试连接失败:', error);

                // 添加测试失败日志
                this.addSyncLog('error', `连接测试失败: ${error.message}`);

                // 显示更详细的错误信息
                const errorDialog = this.createDialog(`
                    <div class="ya-config-form">
                        <h3>连接测试失败</h3>
                        <div class="test-result error">
                            <p>❌ ${this.getErrorMessage(error)}</p>
                            <div class="error-tips">
                                <p>可能的原因：</p>
                                <ul>
                                    <li>账号或密码错误</li>
                                    <li>应用密码已过期</li>
                                    <li>网络连接问题</li>
                                    <li>WebDAV 服务未开启</li>
                                </ul>
                            </div>
                        </div>
                        <div class="form-btns">
                            <button class="ya-btn danger" onclick="this.closest('.ya-config-dialog').remove();document.querySelector('.ya-mask').remove();">关闭</button>
                        </div>
                    </div>
                `);
                document.body.appendChild(errorDialog);
            }
        }

        getErrorMessage(error) {
            if (error.message.includes('401')) {
                return '账号或密码错误';
            } else if (error.message.includes('404')) {
                return '无法访问 WebDAV 服务';
            } else if (error.message.includes('timeout')) {
                return '连接超时';
            }
            return error.message;
        }

        // 修改文件名格式，精确到秒
        getTimeStampString() {
            const date = new Date();
            return `${date.getFullYear()}${(date.getMonth()+1).toString().padStart(2,'0')}${date.getDate().toString().padStart(2,'0')}_${date.getHours().toString().padStart(2,'0')}${date.getMinutes().toString().padStart(2,'0')}${date.getSeconds().toString().padStart(2,'0')}_${date.getMilliseconds().toString().padStart(3,'0')}`;
        }

        // WebDAV 基础操作
        propfind(path, depth = 0) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'PROPFIND',
                    url: this.config.server + '/' + path,
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password),
                        'Depth': depth.toString(),
                        'Content-Type': 'application/xml;charset=UTF-8'
                    },
                    data: '<?xml version="1.0" encoding="utf-8"?><propfind xmlns="DAV:"><prop><getlastmodified/><getcontentlength/><resourcetype/></prop></propfind>',
                    onload: (response) => {
                        if (response.status === 207 || response.status === 200) {
                            resolve(response);
                        } else {
                            reject(new Error(`PROPFIND 失败: ${response.status}`));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }

        getFile(filename) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: this.config.server + '/' + filename,
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password)
                    },
                    onload: (response) => {
                        if (response.status === 200) {
                            resolve(response.responseText);
                        } else if (response.status === 404) {
                            resolve(null);
                        } else {
                            reject(new Error('GET failed: ' + response.status));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }

        async putFile(filename, content) {
            try {
                // 如果是记事本文件，直接保存
                if (filename.startsWith('notes/')) {
                    return await this._putSingleFile(filename, content);
                }
            } catch (error) {
                console.error('putFile error:', error);
                throw error;
            }
        }

        async _putSingleFile(filename, content) {
            return new Promise((resolve, reject) => {
                // 根据文件类型设置Content-Type
                const contentType = filename.endsWith('.txt') ? 'text/plain; charset=utf-8' : 'application/json';

                GM_xmlhttpRequest({
                    method: 'PUT',
                    url: this.config.server + '/' + filename,
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password),
                        'Content-Type': contentType
                    },
                    data: content,
                    onload: (response) => {
                        if (response.status === 201 || response.status === 204) {
                            resolve(response);
                        } else {
                            reject(new Error(`文件上传失败: ${response.status}`));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }

        async createDirectory(path) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'MKCOL',
                    url: this.config.server + '/' + path,
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password)
                    },
                    onload: (response) => {
                        // 201 创建成功，405 目录已存在，都算正常
                        if (response.status === 201 || response.status === 405) {
                            resolve(response);
                        } else {
                            reject(new Error(`创建目录失败: ${response.status}`));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }

        // 列出目录中的文件
        async listFiles(directory) {
            try {
                const response = await this.propfind(directory, 1);
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(response.responseText, 'text/xml');
                const responses = xmlDoc.querySelectorAll('response');

                const files = [];
                responses.forEach(resp => {
                    const href = resp.querySelector('href')?.textContent;
                    const lastModified = resp.querySelector('getlastmodified')?.textContent;
                    const contentLength = resp.querySelector('getcontentlength')?.textContent;
                    const resourceType = resp.querySelector('resourcetype');

                    if (href && !resourceType?.querySelector('collection')) {
                        // 这是一个文件，不是目录
                        const filename = href.split('/').pop();
                        if (filename && filename !== directory) {
                            files.push({
                                name: filename,
                                lastModified: lastModified ? new Date(lastModified) : new Date(),
                                size: contentLength ? this.formatFileSize(parseInt(contentLength)) : '未知大小'
                            });
                        }
                    }
                });

                return files;
            } catch (error) {
                console.error('列出文件失败:', error);
                throw error;
            }
        }

        // 格式化文件大小
        formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 添加同步日志相关方法
        getSyncLogs() {
            return JSON.parse(GM_getValue('sync_logs', '[]'));
        }

        addSyncLog(type, message) {
            const logs = this.getSyncLogs();
            logs.unshift({
                time: new Date().toISOString(),
                type,
                message
            });

            // 只保留最近10条记录
            GM_setValue('sync_logs', JSON.stringify(logs.slice(0, 10)));

            // 如果当前有日志显示，更新它
            const logContent = document.querySelector('.log-content');
            if (logContent) {
                this.updateLogDisplay(logContent);
            }
        }

        async deleteFile(filename) {
            const url = `${this.config.server}/notes/${encodeURIComponent(filename)}`;
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'DELETE',
                    url: url,
                    headers: {
                        'Authorization': 'Basic ' + btoa(`${this.config.username}:${this.config.password}`)
                    },
                    onload: (response) => {
                        if (response.status >= 200 && response.status < 300) {
                            this.addSyncLog('success', `成功删除文件: ${filename}`);
                            resolve();
                        } else {
                            const errorMsg = `删除文件失败: ${response.status} ${response.statusText || ''} - ${response.responseText || ''}`;
                            this.addSyncLog('error', errorMsg);
                            reject(new Error(errorMsg));
                        }
                    },
                    onerror: (error) => {
                        const errorMsg = `删除文件请求错误: ${error.error}`;
                        this.addSyncLog('error', errorMsg);
                        reject(new Error(errorMsg));
                    }
                });
            });
        }

        async deleteNote(dialog, filename) {
            if (!confirm(`确定要删除笔记 "${filename}" 吗？此操作不可逆！`)) {
                return;
            }
            try {
                await this.deleteFile(filename);
                GM_notification({
                    text: `笔记 "${filename}" 已删除！`,
                    timeout: 3000
                });
                // 重新加载笔记列表
                await this.loadNotesList(dialog);
            } catch (error) {
                GM_notification({
                    text: `删除笔记失败: ${this.getErrorMessage(error)}`,
                    timeout: 5000
                });
                console.error('删除笔记失败:', error);
            }
        }

        async uploadFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => {
                    const arrayBuffer = reader.result;
                    const uint8Array = new Uint8Array(arrayBuffer);
                    // 关键：转为二进制字符串，最大兼容Tampermonkey
                    let binaryString = '';
                    for (let i = 0; i < uint8Array.length; i++) {
                        binaryString += String.fromCharCode(uint8Array[i]);
                    }
                    GM_xmlhttpRequest({
                        method: 'PUT',
                        url: this.config.server + '/notes/' + encodeURIComponent(file.name),
                        headers: {
                            'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password),
                            'Content-Type': 'application/octet-stream'
                        },
                        data: binaryString,
                        binary: true,
                        onload: (response) => {
                            if (response.status === 201 || response.status === 204) {
                                resolve();
                            } else {
                                reject(new Error('文件上传失败: ' + response.status));
                            }
                        },
                        onerror: (error) => reject(error)
                    });
                };
                reader.onerror = reject;
                reader.readAsArrayBuffer(file);
            });
        }

        // 在WebDAVSync类中新增downloadFile方法
        async downloadFile(filename) {
            return new Promise((resolve, reject) => {
                GM_xmlhttpRequest({
                    method: 'GET',
                    url: this.config.server + '/notes/' + encodeURIComponent(filename),
                    headers: {
                        'Authorization': 'Basic ' + btoa(this.config.username + ':' + this.config.password)
                    },
                    responseType: 'arraybuffer',
                    onload: (response) => {
                        if (response.status === 200) {
                            resolve(response.response);
                        } else {
                            reject(new Error('下载失败: ' + response.status));
                        }
                    },
                    onerror: (error) => reject(error)
                });
            });
        }
        updateLogDisplay(container) {
            const logs = JSON.parse(GM_getValue('sync_logs', '[]'));
            container.innerHTML = logs.slice(0, 10).map(log => `
                <div class="log-item ${log.type}">
                    <span class="time">${new Date(log.time).toLocaleString()}</span>
                    <span class="message">${log.message}</span>
                </div>
            `).join('');
        }
    }

    class YaNotepadManager {
        constructor() {
            this.webdav = new WebDAVSync();
            this.initUI();

            // 添加同步设置按钮
            GM_registerMenuCommand('记事本', () => this.webdav.showNotepadDialog());
            GM_registerMenuCommand('同步设置', () => this.webdav.showConfigDialog());
        }

        initUI() {
            // 创建容器
            const container = document.createElement('div');
            container.className = 'ya-notepad-container';

            // 创建记事本按钮
            const notepadBtn = document.createElement('button');
            notepadBtn.className = 'ya-btn ya-notepad-btn';
            notepadBtn.textContent = '记事本';
            notepadBtn.onclick = () => this.webdav.showNotepadDialog();
            container.appendChild(notepadBtn);

            // 使用MutationObserver监听DOM变化
            const observer = new MutationObserver((mutations) => {
                // 检查按钮是否还存在
                if (!document.querySelector('.ya-notepad-container')) {
                    // 如果按钮不存在，重新添加到页面
                    if (document.body) {
                        if (document.body.firstChild) {
                            document.body.insertBefore(container, document.body.firstChild);
                        } else {
                            document.body.appendChild(container);
                        }
                    }
                }
            });

            // 开始观察整个文档
            observer.observe(document.documentElement, {
                childList: true,
                subtree: true
            });

            // 初始添加按钮
            if (document.body) {
                if (document.body.firstChild) {
                    document.body.insertBefore(container, document.body.firstChild);
                } else {
                    document.body.appendChild(container);
                }
            } else {
                // 如果body还不存在，等待DOMContentLoaded事件
                document.addEventListener('DOMContentLoaded', () => {
                    document.body.appendChild(container);
                });
            }
        }
    }

    // 初始化记事本管理器
    window.yaNotepadManager = new YaNotepadManager();
})();